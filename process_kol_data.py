import requests
import json
import time
import os
from feishu import get_tenant_access_token, get_obj_token, search_records, list_records

# ===================== 配置区 =====================
API_BASE_URL = "http://54.84.111.234:8000/api/v1"
NOT_FOUND_KOL_FILE = "not_found_kol.txt"
BATCH_SIZE = 50  # 按照文档要求，每批处理10条数据

# ================ 工具函数 ================
def write_not_found_kol(kol_id):
    """将未找到的KOL ID写入txt文件"""
    with open(NOT_FOUND_KOL_FILE, 'a', encoding='utf-8') as f:
        f.write(f"{kol_id}\n")

# ================ 业务函数 ================
def get_kol_exists(kol_id):
    """校验KOL是否存在，存在返回True，不存在返回False"""
    url = f"{API_BASE_URL}/kol/{kol_id}"
    try:
        resp = requests.get(url)
        if resp.status_code == 200:
            data = resp.json()
            return bool(data) and len(data) > 0
    except Exception as e:
        print(f"校验KOL出错: {e}")
    return False

def get_filter_by_name(filter_name):
    """根据filter_name查找Filter，存在返回filter对象，不存在返回None"""
    url = f"{API_BASE_URL}/filter-data/by_name/{filter_name}"
    try:
        resp = requests.get(url)
        if resp.status_code == 200:
            data = resp.json()
            if data and len(data) > 0:
                return data
    except Exception as e:
        print(f"查找Filter出错: {e}")
    return None

def create_filter(filter_name, project_code):
    """新建Filter，返回新建的filter对象"""
    url = f"{API_BASE_URL}/filter-data/"
    payload = {
        "language": "English",
        "gender": "",
        "location": "US",
        "filter_body": {},
        "filter_name": filter_name,
        "project_code": project_code
    }
    try:
        resp = requests.post(url, json=payload)
        if resp.status_code == 200:
            return resp.json()
    except Exception as e:
        print(f"新建Filter出错: {e}")
    return None

def get_kol_filter_association(kol_id, filter_id=None):
    """查询KOL与Filter的关联，返回关联对象列表"""
    url = f"{API_BASE_URL}/filter-kol-associations/?skip=0&limit=100&kol_id={kol_id}"
    try:
        resp = requests.get(url)
        if resp.status_code == 200:
            associations = resp.json()
            # 如果指定了filter_id，则过滤出匹配的关联
            if filter_id is not None:
                return [a for a in associations if a.get('filter_id') == filter_id]
            return associations
    except Exception as e:
        print(f"查询KOL与Filter关联出错: {e}")
    return []

def create_kol_filter_association(filter_id, kol_id, project_code):
    """新建KOL与Filter的关联关系"""
    url = f"{API_BASE_URL}/filter-kol-associations/"
    payload = {
        "filter_id": filter_id,
        "kol_id": kol_id,
        "project_code": project_code
    }
    try:
        resp = requests.post(url, json=payload)
        if resp.status_code == 200:
            return resp.json()
    except Exception as e:
        print(f"新建KOL与Filter关联出错: {e}")
    return None

# ================ 主处理流程 ================
def process_kol_batch(kol_data_batch, project_code):
    """
    按照数据清洗文档要求处理一批KOL数据
    kol_data_batch: List[dict]，每个dict至少包含kol_id, filter_name
    project_code: str
    返回: dict 包含处理统计信息
    """
    print(f"\n开始处理批次，包含 {len(kol_data_batch)} 条数据")

    # 统计信息
    stats = {
        'total': len(kol_data_batch),
        'processed': 0,
        'kol_not_found': 0,
        'filter_created': 0,
        'association_created': 0,
        'association_exists': 0,
        'errors': 0
    }

    for i, item in enumerate(kol_data_batch, 1):
        print(f"\n--- 处理第 {i} 条数据 ---")

        # 获取原始kol_id和filter_name
        raw_kol_id = item.get('kol_id')
        filter_name = item.get('filter_name')

        if not raw_kol_id or not filter_name:
            print(f"缺少必要字段，跳过: kol_id={raw_kol_id}, filter_name={filter_name}")
            stats['errors'] += 1
            continue

        # 按照文档要求，给kol_id添加TK_前缀
        kol_id = f"TK_{raw_kol_id}"
        print(f"处理 KOL: {kol_id}, Filter: {filter_name}")

        # 步骤2.1: 校验KOL是否存在
        print(f"2.1 校验KOL是否存在: {kol_id}")
        if not get_kol_exists(kol_id):
            print(f"   KOL不存在，记录到文件并跳过: {kol_id}")
            write_not_found_kol(kol_id)
            stats['kol_not_found'] += 1
            continue
        print(f"   KOL存在，继续处理")

        # 步骤2.2: 校验Filter是否存在
        print(f"2.2 校验Filter是否存在: {filter_name}")
        filter_obj = get_filter_by_name(filter_name)
        if not filter_obj:
            print(f"   Filter不存在，按照文档要求创建: {filter_name}")
            filter_obj = create_filter(filter_name, project_code)
            if not filter_obj:
                print(f"   Filter创建失败，跳过: {filter_name}")
                stats['errors'] += 1
                continue
            print(f"   Filter创建成功")
            stats['filter_created'] += 1
        else:
            print(f"   Filter已存在，继续处理")

        filter_id = filter_obj.get('id') or filter_obj.get('filter_id')
        print(f"   获取到 filter_id: {filter_id}")

        # 步骤2.3: 校验KOL与Filter的关联关系
        print(f"2.3 校验KOL与Filter的关联关系")
        # 按照文档要求，检查特定filter_id的关联
        specific_associations = get_kol_filter_association(kol_id, filter_id)
        if specific_associations:
            print(f"   KOL与该Filter已存在关联，跳过: {kol_id} <-> {filter_id}")
            stats['association_exists'] += 1
            continue
        print(f"   KOL与该Filter尚未关联，需要建立关联")

        # 步骤2.4: 新建 KOL 与 Filter 的关联关系
        print(f"2.4 新建 KOL 与 Filter 的关联关系")
        result = create_kol_filter_association(filter_id, kol_id, project_code)
        if result:
            print(f"   成功建立关联: {kol_id} <-> {filter_id}")
            stats['association_created'] += 1
        else:
            print(f"   建立关联失败: {kol_id} <-> {filter_id}")
            stats['errors'] += 1

        stats['processed'] += 1
        # 添加小延迟，避免请求过快
        time.sleep(0.1)

    # 打印批次处理统计
    print(f"\n=== 批次处理统计 ===")
    print(f"总数: {stats['total']}")
    print(f"已处理: {stats['processed']}")
    print(f"KOL不存在: {stats['kol_not_found']}")
    print(f"创建的Filter: {stats['filter_created']}")
    print(f"创建的关联: {stats['association_created']}")
    print(f"已存在的关联: {stats['association_exists']}")
    print(f"错误数: {stats['errors']}")

    return stats

def fetch_kol_data_from_feishu(access_token, app_id, table_id, batch_size=10, conditions=None):
    """
    分批从飞书多维表格获取KOL数据，返回生成器，每次yield一批数据
    使用正确的分页机制确保获取所有数据，每次只获取指定batch_size数量的记录
    """
    page_token = None
    has_more = True
    batch_count = 0

    # 添加调试信息
    print(f"开始查询飞书表格，batch_size={batch_size}")
    print(f"查询条件: {conditions}")

    while has_more:
        batch_count += 1
        print(f"\n=== 执行第 {batch_count} 次API查询 ===")
        print(f"当前 page_token: {page_token}")

        # 使用list_records而不search_records来避免分页问题
        res = list_records(
            access_token=access_token,
            app_id=app_id,
            table_id=table_id,
            page_size=batch_size,  # 明确设置每页大小为batch_size
            page_token=page_token
        )

        print(f"API响应状态: {res.get('code')}")

        if res.get('code') != 0:
            print(f"飞书API查询失败: {res.get('msg')}")
            break

        data = res.get('data', {})
        items = data.get('items', [])
        total = data.get('total', 0)

        print(f"本次查询返回记录数: {len(items)}")
        print(f"表格总记录数: {total}")

        if not items:
            print("没有更多数据")
            break

        batch = []
        for i, record in enumerate(items):
            fields = record.get('fields', {})
            record_id = record.get('record_id', 'unknown')
            kol_id = None
            filter_name = None

            # # 调试：打印前几条记录的详细信息
            # if i < 2:
            #     print(f"记录 {i+1} (record_id: {record_id}): {fields}")

            # 这里假设表格有KOL ID和Filter Name字段
            if 'KOL ID' in fields and fields['KOL ID']:
                kol_id_value = fields['KOL ID'][0]['text'] if isinstance(fields['KOL ID'], list) else fields['KOL ID']
                kol_id = kol_id_value['text'] if isinstance(kol_id_value, dict) and 'text' in kol_id_value else kol_id_value
            if 'Filter' in fields and fields['Filter']:
                filter_name_value = fields['Filter'][0]['text'] if isinstance(fields['Filter'], list) else fields['Filter']
                filter_name = filter_name_value['text'] if isinstance(filter_name_value, dict) and 'text' in filter_name_value else filter_name_value
            if kol_id and filter_name:
                batch.append({"kol_id": kol_id, "filter_name": filter_name, "record_id": record_id})

        print(f"解析出有效数据: {len(batch)} 条")

        if batch:
            # 每次只yield一批数据，批次大小不超过batch_size
            yield batch

        # 处理分页，获取下一页的page_token
        has_more = data.get('has_more', False)
        new_page_token = data.get('page_token')

        print(f"has_more: {has_more}")
        print(f"new_page_token: {new_page_token}")

        # 检查page_token是否发生变化
        if new_page_token == page_token:
            print("警告: page_token没有变化，可能陷入无限循环")
            break

        page_token = new_page_token

        if not has_more:
            print("已到达最后一页")
            break

        if not page_token:
            print("page_token为空，结束查询")
            break

        # 避免请求过快
        time.sleep(0.5)

    print(f"\n查询完成，共执行 {batch_count} 次API调用")

# ================ 测试函数 ================
def mock_search_records(access_token, app_id, table_id, conditions, conjunction="or", page_size=10, page_token=None):
    """
    模拟飞书API的search_records函数，用于测试分页功能
    """
    # 模拟总共有25条数据
    total_records = 25

    # 模拟数据
    all_records = []
    for i in range(total_records):
        record = {
            "fields": {
                "KOL ID": f"kol_{i}",
                "Filter": f"filter_{i % 5}"  # 5种不同的filter
            }
        }
        all_records.append(record)

    # 处理分页
    start_idx = 0
    if page_token:
        # 简单的分页逻辑，page_token就是起始索引
        start_idx = int(page_token)

    end_idx = min(start_idx + page_size, total_records)
    current_page_records = all_records[start_idx:end_idx]

    # 判断是否有更多页
    has_more = end_idx < total_records
    next_page_token = str(end_idx) if has_more else None

    # 构造返回结果
    result = {
        "code": 0,
        "data": {
            "items": current_page_records,
            "has_more": has_more,
            "page_token": next_page_token,
            "total": total_records
        }
    }

    return result

def test_pagination():
    """
    测试分页功能
    """
    print("开始测试分页功能...")

    # 创建一个自定义的fetch_kol_data_from_feishu函数，使用mock_search_records
    def test_fetch_kol_data(access_token, app_id, table_id, batch_size=10, conditions=None):
        """
        使用mock_search_records的fetch_kol_data_from_feishu版本
        """
        page_token = None
        has_more = True

        while has_more:
            res = mock_search_records(
                access_token=access_token,
                app_id=app_id,
                table_id=table_id,
                conditions=conditions or [],
                page_size=batch_size,
                page_token=page_token
            )

            if res.get('code') != 0:
                print(f"飞书API查询失败: {res.get('msg')}")
                break

            items = res.get('data', {}).get('items', [])
            if not items:
                break

            batch = []
            for record in items:
                fields = record.get('fields', {})
                kol_id = fields.get('KOL ID')
                filter_name = fields.get('Filter')
                if kol_id and filter_name:
                    batch.append({"kol_id": kol_id, "filter_name": filter_name})

            if batch:
                yield batch

            # 处理分页
            has_more = res.get('data', {}).get('has_more', False)
            page_token = res.get('data', {}).get('page_token')

            if not has_more or not page_token:
                break

    # 测试参数
    access_token = "mock_token"
    app_id = "mock_app_id"
    table_id = "mock_table_id"
    batch_size = 8  # 每页8条，总共25条，应该有4页

    # 执行测试
    batch_count = 0
    total_records = 0

    for batch in test_fetch_kol_data(access_token, app_id, table_id, batch_size=batch_size):
        batch_count += 1
        total_records += len(batch)
        print(f"测试批次 #{batch_count}: 大小={len(batch)}, 累计记录数={total_records}")
        print(f"批次内容示例: {batch[:2] if len(batch) >= 2 else batch}")

    print(f"测试完成，共处理 {batch_count} 个批次，总计 {total_records} 条记录")

    # 验证结果
    expected_batches = 4  # 25条数据，每页8条，应该有4页（8+8+8+1）
    assert batch_count == expected_batches, f"期望 {expected_batches} 个批次，实际 {batch_count} 个批次"
    assert total_records == 25, f"期望 25 条记录，实际 {total_records} 条记录"

    print("测试通过！")

def main(table_id):
    """
    主函数：从飞书表格批量获取数据并执行数据清洗
    """
    # 飞书API参数（请根据实际情况填写）
    app_id = "cli_a50254e4e43bd013"
    app_secret = "0PStyxmXLUnUKq5KvS5iCbmg8z4z6nkJ"
    app_wiki_token = 'QTRqwY8vFiplVUkP5CMc9e9Fn1f'  # 通过API Explorer获取
    # table_id = 'tbla9MrmdY17iAXM'
    project_code = "OOG120"  # 按照数据清洗文档要求
    # 可根据需要自定义筛选条件
    # 添加一个基本条件来避免空查询
    conditions = [
        {
            "field_name": "KOL ID",
            "operator": "isNotEmpty",
            "value": []
        }
    ]

    # 获取access_token
    token = get_tenant_access_token(app_id, app_secret)
    access_token = token['tenant_access_token']
    # 获取obj_token
    app_token = get_obj_token(app_access_token=access_token, token=app_wiki_token)
    obj_token = app_token['data']['node']['obj_token']

    print("准备开始")

    # 批量获取并处理数据
    batch_count = 0
    total_records = 0

    # 总体统计
    total_stats = {
        'total': 0,
        'processed': 0,
        'kol_not_found': 0,
        'filter_created': 0,
        'association_created': 0,
        'association_exists': 0,
        'errors': 0
    }

    print(f"\n开始按照数据清洗文档要求处理数据...")
    print(f"项目代码: {project_code}")
    print(f"每批处理数量: {BATCH_SIZE}")

    for batch in fetch_kol_data_from_feishu(access_token, obj_token, table_id, batch_size=BATCH_SIZE, conditions=conditions):
        batch_count += 1
        total_records += len(batch)
        print(f"\n========== 批次 #{batch_count} ===========")
        print(f"批次大小: {len(batch)}, 累计记录数: {total_records}")
        print(f"批次内容示例: {batch[:2] if len(batch) >= 2 else batch}")

        # 按照数据清洗文档要求处理数据
        batch_stats = process_kol_batch(batch, project_code)

        # 累计统计
        for key in total_stats:
            total_stats[key] += batch_stats[key]

        print(f"\n批次 #{batch_count} 处理完成")
        time.sleep(1)  # 批次间隔延迟

    # 打印总体统计
    print(f"\n" + "="*50)
    print(f"数据清洗完成 - 总体统计")
    print(f"="*50)
    print(f"处理批次数: {batch_count}")
    print(f"总记录数: {total_stats['total']}")
    print(f"成功处理: {total_stats['processed']}")
    print(f"KOL不存在: {total_stats['kol_not_found']}")
    print(f"创建的Filter: {total_stats['filter_created']}")
    print(f"创建的关联: {total_stats['association_created']}")
    print(f"已存在的关联: {total_stats['association_exists']}")
    print(f"错误数: {total_stats['errors']}")
    print(f"\n不存在的KOL已记录在: {NOT_FOUND_KOL_FILE}")
    print(f"="*50)

if __name__ == "__main__":
    table_ids = ['tblL7YsTpfFwiSvG', 'tblcIuGkxxR4Rvyw', 'tblpubK6Nu9Np6Uy', 'tblEO0zc6bB2CZxz']
    # table_ids = ['tbla9MrmdY17iAXM', 'tbl5BetO1a0GZgGk', 'tblKLUXlMU1GLeCV', 'tblR2bRbMTVG2XvM']
    # 运行实际的处理逻辑
    for table_id in table_ids:
        main(table_id)

    # 运行测试
    # test_pagination()
